# frozen_string_literal: true

require "bundler/setup"

Bundler.require

ENV["RAILS_ENV"] = "development"

require "action_controller"
require "rack/cors"

class RailsMiniApp < Rails::Application
  config.hosts = nil
  config.secret_key_base = "test_secret_key_base_for_rails_mini_app"
  config.eager_load = false
  config.logger = Logger.new($stdout)
  config.log_level = :debug
  config.api_only = true
  config.force_ssl = false

  # Add CORS middleware using Rack::Cors
  config.middleware.insert_before 0, Rack::Cors do
    allow do
      origins '*'
      resource '*',
        headers: :any,
        methods: :any,
        expose: :any,
        credentials: false
    end
  end

  initializer :configure_sentry do
    Sentry.init do |config|
      config.dsn = ENV["SENTRY_DSN"]
      config.breadcrumbs_logger = [:active_support_logger, :http_logger, :redis_logger]
      config.traces_sample_rate = 1.0
      config.send_default_pii = true
      config.sdk_logger.level = ::Logger::DEBUG
      config.sdk_logger = Sentry::Logger.new($stdout)
      config.debug = true
      config.include_local_variables = true
      config.release = "sentry-ruby-rails-mini-#{Time.now.utc}"

      config.transport.transport_class = Sentry::DebugTransport
      config.background_worker_threads = 0
    end
  end
end

class ErrorController < ActionController::Base
  def error
    # Let the exception bubble up so Sentry Rails integration captures it with proper context
    result = 1 / 0
    render json: { result: result }
  end

  def options
    head :ok
  end
end

class EventsController < ActionController::Base
  def health
    render json: {
      status: "ok",
      timestamp: Time.now.utc.iso8601,
      sentry_initialized: Sentry.initialized?
    }
  end

  def trace_headers
    headers = Sentry.get_trace_propagation_headers || {}
    render json: { headers: headers }
  end

  def options
    head :ok
  end
end

RailsMiniApp.initialize!

RailsMiniApp.routes.draw do
  get '/health', to: 'events#health'
  get '/error', to: 'error#error'
  get '/trace_headers', to: 'events#trace_headers'

  # Handle OPTIONS preflight requests explicitly
  options '/health', to: 'events#options'
  options '/error', to: 'error#options'
  options '/trace_headers', to: 'events#options'

  # Catch-all OPTIONS handler for any other paths
  match '*path', to: proc { |env|
    [200, {
      'Access-Control-Allow-Origin' => '*',
      'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers' => 'Content-Type, Authorization, sentry-trace, baggage',
      'Content-Type' => 'application/json'
    }, ['{"status": "ok"}']]
  }, via: :options
end

if __FILE__ == $0
  require "rack"
  require "rack/handler/puma"

  port = ENV.fetch("SENTRY_E2E_RAILS_APP_PORT", "4000").to_i
  Rack::Handler::Puma.run(RailsMiniApp, Host: "0.0.0.0", Port: port)
end
