import { defineConfig } from 'vite'
import { svelte } from '@sveltejs/vite-plugin-svelte'

export default defineConfig({
  plugins: [
    svelte(),
    {
      name: 'health-check',
      configureServer(server) {
        server.middlewares.use('/health', (req, res, next) => {
          if (req.method === 'GET') {
            res.setHeader('Content-Type', 'application/json')
            res.setHeader('Access-Control-Allow-Origin', '*')

            // Check if Sentry environment variables are properly configured
            const sentryDsn = process.env.SENTRY_DSN_JS
            const railsAppUrl = process.env.SENTRY_E2E_RAILS_APP_URL

            if (sentryDsn && railsAppUrl) {
              res.end(JSON.stringify({
                status: 'ok',
                timestamp: new Date().toISOString(),
                service: 'svelte-mini',
                sentry_dsn_configured: !!sentryDsn,
                rails_app_url: railsAppUrl
              }))
            } else {
              res.statusCode = 500
              res.end(JSON.stringify({
                status: 'error',
                timestamp: new Date().toISOString(),
                service: 'svelte-mini',
                error: 'Sentry configuration incomplete',
                debug_info: {
                  sentry_dsn_configured: !!sentryDsn,
                  sentry_dsn_value: sentryDsn || 'not set',
                  rails_app_url_configured: !!railsAppUrl,
                  rails_app_url_value: railsAppUrl || 'not set'
                }
              }))
            }
          } else {
            next()
          }
        })
      }
    }
  ],
  server: {
    port: parseInt(process.env.SENTRY_E2E_SVELTE_APP_PORT),
    host: '0.0.0.0',
  },
  define: {
    SENTRY_E2E_RAILS_APP_URL: JSON.stringify(process.env.SENTRY_E2E_RAILS_APP_URL)
  },
  envPrefix: ['VITE_', 'SENTRY_']
})
