name: e2e tests

on:
  workflow_dispatch:
  push:
    branches:
      - master
  pull_request:

permissions:
  contents: read

concurrency:
  group: e2e-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  e2e-tests:
    name: e2e tests
    runs-on: ubuntu-latest
    timeout-minutes: 5

    strategy:
      fail-fast: false
      matrix:
        ruby_version: ["3.4.5"]

    env:
      DOCKER_IMAGE: "ghcr.io/getsentry/sentry-ruby-devcontainer-3.4"
      DOCKER_TAG: "latest"

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up `.env` file
        run: |
          cd .devcontainer
          cp .env.example .env

      - name: Restore rubygems cache
        uses: actions/cache@v3
        with:
          path: vendor/gems
          key: ${{ runner.os }}-${{ matrix.ruby_version }}-gems-${{ hashFiles('Gemfile.lock', '*/Gemfile.lock', 'spec/apps/**/Gemfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.ruby_version }}-gems-

      - name: Restore node_modules cache
        uses: actions/cache@v3
        with:
          path: spec/apps/svelte-mini/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('spec/apps/svelte-mini/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Set up test container
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            run --rm sentry-test \
            echo "Done"

      - name: Start test services
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            up -d sentry-test

      - name: "Wait for rails-mini app to be ready"
        uses: nev7n/wait_for_response@v1
        with:
          url: 'http://localhost:4000/health'
          responseCode: 200
          timeout: 90000
          interval: 500

      - name: "Wait for svelte-mini app to be ready"
        uses: nev7n/wait_for_response@v1
        with:
          url: 'http://localhost:4001/health'
          responseCode: 200
          timeout: 90000
          interval: 500

      - name: Run e2e tests via sentry-test
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            exec sentry-test \
            bundle exec rake

      - name: Stop e2e services
        if: always()
        run: docker compose --file .devcontainer/docker-compose.yml down

      - name: Upload test artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-logs-ruby-${{ matrix.ruby_version }}
          path: |
            log/sentry_debug_events.log
          retention-days: 7
