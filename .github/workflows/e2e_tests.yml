name: e2e tests

on:
  workflow_dispatch:
  push:
    branches:
      - master
  pull_request:

permissions:
  contents: read

concurrency:
  group: e2e-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  e2e-tests:
    name: e2e tests
    runs-on: ubuntu-latest
    timeout-minutes: 5

    strategy:
      fail-fast: false
      matrix:
        ruby_version: ["3.4.5"]

    env:
      DOCKER_IMAGE: "ghcr.io/getsentry/sentry-ruby-devcontainer-3.4"
      DOCKER_TAG: "latest"

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up `.env` file
        run: |
          cd .devcontainer
          cp .env.example .env
          echo "=== Verifying .env file was created ==="
          ls -la .env
          echo "=== .env file contents ==="
          cat .env

      - name: Restore rubygems cache
        uses: actions/cache@v3
        with:
          path: vendor/gems
          key: ${{ runner.os }}-${{ matrix.ruby_version }}-gems-${{ hashFiles('Gemfile.lock', '*/Gemfile.lock', 'spec/apps/**/Gemfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.ruby_version }}-gems-

      - name: Clean npm for fresh install
        run: |
          echo "=== Cleaning npm cache and node_modules for architecture compatibility ==="
          rm -rf spec/apps/svelte-mini/node_modules
          rm -f spec/apps/svelte-mini/package-lock.json
          npm cache clean --force
          echo "=== System info ==="
          echo "Architecture: $(uname -m)"
          echo "Platform: $(uname -s)"
          echo "Node.js: $(node --version)"
          echo "npm: $(npm --version)"

      - name: Set up test container
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            run --rm sentry-test \
            echo "Done"

      - name: Fix Rollup native module issue
        run: |
          echo "=== Manually installing rollup native module for Linux x64 ==="
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            run --rm sentry-test \
            bash -c "cd spec/apps/svelte-mini && npm install @rollup/rollup-linux-x64-gnu --save-optional || echo 'Rollup module install failed, but continuing'"

      - name: Start test services
        run: |
          echo "=== Starting test services ==="
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            up -d sentry-test
          echo "=== Services started, waiting 5 seconds ==="
          sleep 5

      - name: Debug - Check container status
        run: |
          echo "=== Docker container status ==="
          docker compose --file .devcontainer/docker-compose.yml ps
          echo ""
          echo "=== Container inspect (command and status) ==="
          docker inspect devcontainer-sentry-test-1 --format='{{.Config.Cmd}} | {{.State.Status}} | {{.State.ExitCode}}' || echo "Container inspect failed"
          echo ""
          echo "=== Environment file contents ==="
          cat .devcontainer/.env
          echo ""
          echo "=== Docker container logs (last 100 lines) ==="
          docker compose --file .devcontainer/docker-compose.yml logs --tail=100 sentry-test
          echo ""
          echo "=== Environment variables in container ==="
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            exec sentry-test env | grep -E "(SENTRY|PORT)" || true

      - name: Debug - Check network connectivity
        run: |
          echo "=== Network connectivity tests ==="
          echo "Checking if ports are listening..."
          netstat -tlnp | grep -E ':(4000|4001)' || echo "No services listening on ports 4000/4001"
          echo ""
          echo "Checking Docker port mapping..."
          docker port devcontainer-sentry-test-1 || echo "No port mappings found"
          echo ""
          echo "Checking if we can reach the health endpoints..."
          echo "--- Rails health endpoint ---"
          curl -v --max-time 10 http://localhost:4000/health || echo "Failed to reach Rails health endpoint"
          echo ""
          echo "--- Svelte health endpoint ---"
          curl -v --max-time 10 http://localhost:4001/health || echo "Failed to reach Svelte health endpoint"
          echo ""

      - name: Debug - Check processes inside container
        run: |
          echo "=== Processes running inside container ==="
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            exec sentry-test ps aux
          echo ""
          echo "=== Check if foreman is running ==="
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            exec sentry-test pgrep -f foreman || echo "Foreman not found"
          echo ""
          echo "=== Check if Rails app (puma) is running ==="
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            exec sentry-test pgrep -f puma || echo "Puma not found"
          echo ""
          echo "=== Check if Svelte app (vite) is running ==="
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            exec sentry-test pgrep -f vite || echo "Vite not found"

      - name: Wait for services to start
        run: |
          echo "=== Waiting 30 seconds for services to fully start ==="
          sleep 30
          echo "=== Checking container logs after wait ==="
          docker compose --file .devcontainer/docker-compose.yml logs --tail=20 sentry-test

      - name: Debug - Test health endpoints manually
        run: |
          echo "=== Testing health endpoints manually ==="
          echo "--- Testing Rails health endpoint ---"
          curl -f --max-time 5 http://localhost:4000/health && echo "Rails health OK" || echo "Rails health FAILED"
          echo ""
          echo "--- Testing Svelte health endpoint ---"
          curl -f --max-time 5 http://localhost:4001/health && echo "Svelte health OK" || echo "Svelte health FAILED"
          echo ""

      - name: "Wait for rails-mini app to be ready"
        uses: nev7n/wait_for_response@v1
        with:
          url: 'http://localhost:4000/health'
          responseCode: 200
          timeout: 90000
          interval: 500

      - name: "Wait for svelte-mini app to be ready"
        uses: nev7n/wait_for_response@v1
        with:
          url: 'http://localhost:4001/health'
          responseCode: 200
          timeout: 90000
          interval: 500

      - name: Debug - Final health check if wait failed
        if: failure()
        run: |
          echo "=== Health check failed - gathering final debug info ==="
          echo "--- Container status ---"
          docker compose --file .devcontainer/docker-compose.yml ps
          echo ""
          echo "--- Recent container logs ---"
          docker compose --file .devcontainer/docker-compose.yml logs --tail=50 sentry-test
          echo ""
          echo "--- Final health endpoint tests ---"
          curl -v --max-time 5 http://localhost:4000/health || echo "Rails health still failing"
          curl -v --max-time 5 http://localhost:4001/health || echo "Svelte health still failing"
          echo ""
          echo "--- Process list ---"
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            exec sentry-test ps aux || echo "Could not get process list"

      - name: Run e2e tests via sentry-test
        run: |
          docker compose \
            --file .devcontainer/docker-compose.yml \
            --env-file .devcontainer/.env \
            exec sentry-test \
            bundle exec rake

      - name: Stop e2e services
        if: always()
        run: docker compose --file .devcontainer/docker-compose.yml down

      - name: Upload test artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-logs-ruby-${{ matrix.ruby_version }}
          path: |
            log/sentry_debug_events.log
          retention-days: 7
